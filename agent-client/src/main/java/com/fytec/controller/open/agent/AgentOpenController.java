package com.fytec.controller.open.agent;

import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.fastjson.JSONObject;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.aspect.annotation.ExternalServiceLog;
import com.fytec.controller.open.agent.flow.service.AgentOpenService;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.Output;
import com.fytec.controller.open.agent.flow.utils.sft.SftKnowledgeDto;
import com.fytec.dto.agent.AgentPublishDTO;
import com.fytec.dto.agent.AgentPublishHistoryDTO;
import com.fytec.dto.agent.feedback.AgentHistoryFeedbackDTO;
import com.fytec.dto.doc.DocParseDTO;
import com.fytec.dto.doc.PaperAnalysisDTO;
import com.fytec.dto.flow.WorkflowExecuteDTO;
import com.fytec.dto.doc.PaperCutDTO;
import com.fytec.dto.open.EchartFormatDTO;
import com.fytec.dto.open.SqlEchartFormatDTO;
import com.fytec.dto.open.Markdown2wordDTO;
import com.fytec.dto.plugin.PluginFileParseDTO;
import com.fytec.entity.agent.AgentPublishHistory;
import com.fytec.file.FileParseUtils;
import com.fytec.file.baiduyun.BaiduyunDocParserClient;
import com.fytec.service.agent.AgentSftService;
import com.fytec.service.flow.WorkflowService;
import com.fytec.util.UUIDGenerator;
import com.fytec.utils.Markdown2docxUtil;
import jakarta.validation.constraints.NotBlank;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.service.AgentFlowService;
import com.fytec.controller.open.agent.dto.CallAgentDTO;
import com.fytec.service.agent.AgentService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.constraints.NotNull;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.fytec.file.FileParseUtils.FILE_TYPE;


@Slf4j
@Validated
@Tag(name = "智能体对外服务")
@RequestMapping("/api/agent/open")
@RestController
@AllArgsConstructor
public class AgentOpenController {
    private final WorkflowService workflowService;

    private final AgentService agentService;

    private final AgentFlowService agentFlowService;

    private final AgentOpenService agentOpenService;

    private final BaiduyunDocParserClient baiduyunDocParserClient;

    private final AgentSftService agentSftService;


    @PostMapping(value = "/stream/execute")
    @Operation(summary = "调用智能体-流式")
    @SaCheckClientToken(scope = "client:execute")
    public void executePublishedAgentStream(HttpServletResponse response,
                                            @Validated @RequestBody CallAgentDTO callAgentDTO) {
        if (StringUtils.isBlank(callAgentDTO.getRawInput())) {
            callAgentDTO.setRawInput(callAgentDTO.getUserInput());
        }
        try {
            agentFlowService
                    .executePublishedAgent(response, callAgentDTO);
            log.info("stream execute success");
        } catch (AgentHandlerError e) {
            e.printStackTrace();
            throw new ValidateException(e.getErrorMsg());
        }
    }

//    @PostMapping(value = "/stream/execute/query-more")
//    @Operation(summary = "调用智能体-流式-查询更多")
//    @SaCheckClientToken(scope = "client:execute")
//    public void executePublishedAgentStreamMoreQuery(HttpServletResponse response,String) {
//
//    }

    @PostMapping(value = "/non-stream/execute")
    @Operation(summary = "调用智能体-非流式，其他业务平台需要用的，非流默认不需要面向用户（流程中都是非流的）")
    @SaCheckClientToken(scope = "client:execute")
//    @SaCheckPermission4FytecClient(value = "client:execute", orRole = "admin")
    public R executePublishedAgent(@Validated @RequestBody
                                   CallAgentDTO callAgentDTO) {

        AgentPublishHistoryDTO history = null;
        if (StringUtils.isBlank(callAgentDTO.getRawInput())) {
            callAgentDTO.setRawInput(callAgentDTO.getUserInput());
        }
        try {
            history = agentFlowService.executePublishedAgent(null, callAgentDTO);
            log.info("non-stream execute success");
            return R.ok(history);
        } catch (AgentHandlerError e) {
            return R.failed(e.getErrorMsg() + ":" + e.getMessage());

        }
    }

    @GetMapping("/stream/reflush")
    @Operation(summary = "智能体直接对话[流返回,重新生成]")
    @SaCheckClientToken(scope = "client:execute")
    public void executePublishedAgentReflush(HttpServletResponse response,
                                             String thirdUserToken,
                                             @NotBlank Long historyId
    ) throws Exception {
        agentFlowService.executePublishedAgentReflush(response, thirdUserToken, historyId);
    }

    @GetMapping("/non-stream/reflush")
    @Operation(summary = "智能体直接对话[流返回,重新生成]")
    @SaCheckClientToken(scope = "client:execute")
    public R executePublishedAgentReflush(@NotBlank Long historyId,
                                          String thirdUserToken
    ) {
        try {
            AgentPublishHistoryDTO dto = agentFlowService.executePublishedAgentReflush(null, thirdUserToken, historyId);
            return R.ok(dto);
        } catch (AgentHandlerError e) {
            return R.failed(e.getErrorMsg() + ":" + e.getMessage());
        }
    }

    @GetMapping("/agent/detail")
    @Operation(summary = "根据智能体id查到最新发布的智能体配置（开场白等）")
    @SaCheckClientToken(scope = "client:execute")
    public R<AgentPublishDTO> publishedAgentDetail(
            @NotNull(message = "智能体ID不能为空") Long agentId
    ) throws Exception {
        // 根据智能体id查到最新发布的智能体配置（开场白等）
        AgentPublishDTO agentPublishDTO = agentOpenService.publishedAgentDetail(agentId);
        // 返回成功
        return R.ok(agentPublishDTO);
    }

    @GetMapping("/chat/clear")
    @Operation(summary = "智能体会话清除")
    @SaCheckClientToken(scope = "client:execute")
//    @SaCheckPermission4FytecClient(value = "client:execute", orRole = "admin")
//    @NotNull(message = "智能体会话ID不能为空")
    public R<Void> executePublishedAgentReflush(
            String conversationId,// 为空就是清空全部
            @NotNull(message = "智能体ID不能为空") Long agentId,
            @NotNull(message = "用户ID不能为空") String thirdUserId
    ) throws Exception {
        agentFlowService.clearPublishHistory(conversationId, agentId, thirdUserId);
        return R.ok();
    }

    @GetMapping(value = "/chat/history")
    @Operation(summary = "智能体对话历史")
    @SaCheckClientToken(scope = "client:execute")
//    @SaCheckPermission4FytecClient(value = "client:execute", orRole = "admin")
    public R<Page<AgentPublishHistoryDTO>> queryPublishedAgentHistory(@NotNull(message = "智能体会话ID不能为空")
                                                                      String conversationId,
                                                                      @NotNull(message = "用户ID不能为空")
                                                                      String thirdUserId,
                                                                      @NotNull(message = "智能体ID不能为空")
                                                                      Long agentId,
                                                                      String objectId,
                                                                      String keyword,
                                                                      Page<AgentPublishHistory> page) {
        return R.ok(agentFlowService.queryAgentHistory(page, conversationId, agentId, thirdUserId, objectId, keyword));
    }

    @GetMapping(value = "/chat/history/detail")
    @Operation(summary = "智能体对话历史详情")
    @SaCheckClientToken(scope = "client:execute")
    public R<AgentPublishHistoryDTO> queryPublishedAgentHistoryDetail(
            @NotNull(message = "历史ID不能为空")
            Long historyId) {
        return R.ok(agentFlowService.queryPublishedAgentHistoryDetail(historyId));
    }


    @GetMapping(value = "/chat/group")
    @Operation(summary = "智能体对话历史组")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<Map<String, Object>>> queryPublishedAgentHistoryGroup(
            @NotNull(message = "用户id不为空") String thirdUserId,
            @NotNull(message = "智能体ID不能为空") Long agentId,
            String startDate,
            String endDate,
            String keyword,
            String objectId
    ) {
        return R.ok(agentFlowService.queryPublishedAgentHistoryGroup(thirdUserId,
                agentId, startDate, endDate, keyword, objectId));
    }

    @PostMapping(value = "/sft/upload")
    @Operation(summary = "监督强化学习-正/负样本")
    @SaCheckClientToken(scope = "client:execute")
    public R<Page<AgentPublishHistoryDTO>> sftLogKnowledgePositive(@Validated @RequestBody
                                                                   SftKnowledgeDto dto) {
//        dto.setType(Constants.KNOWLEDGE_BASE_TYPE.positive.name());
        agentFlowService.sftLogKnowledgePositiveOrNegative(dto);
        return R.ok();
    }

    @GetMapping(value = "/data/query/more")
    @Operation(summary = "查询更多数据集")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<Map<String, Object>>> queryMoreData(
            @NotBlank(message = "历史id不能为空") Long historyId,
            Integer index,// 多个结果集的索引
            @NotBlank(message = "第三方用户id") String thirdUserId) {
        return R.ok(agentFlowService.queryMoreData(thirdUserId, historyId,index));
    }

    @GetMapping(value = "/data/query/detail")
    @Operation(summary = "将某个数字具体展开问")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<Map<String, Object>>> queryDetailData(
            @NotBlank(message = "历史id不能为空") Long historyId,
            @NotBlank(message = "第三方用户id") String thirdUserId,
            @NotBlank(message = "x轴名称（value的解释）") String xLabel,
            @NotBlank(message = "y轴坐标（年份、种类的）") String yLabel,
            @NotBlank(message = "具体value") String value) {

        return R.ok(agentFlowService.queryDetailData(thirdUserId, historyId, xLabel, yLabel, value));
    }


    @PostMapping(value = "/non-stream/execute/{agentId}")
    @Operation(summary = "调用智能体-非流式，其他业务平台需要用的，非流默认不需要面向用户（流程中都是非流的）")
    @SaCheckClientToken(scope = "client:execute")
    public R executePublishedAgentById(@Validated @RequestBody CallAgentDTO callAgentDTO,
                                       @NotBlank @PathVariable String agentId) {
        if (StringUtils.isNotBlank(agentId))
            callAgentDTO.setAgentId(agentId);
        AgentPublishHistoryDTO history = null;
        try {
            history = agentFlowService.executePublishedAgent(null, callAgentDTO);
            log.info("non-stream execute success");
            return R.ok(history);
        } catch (AgentHandlerError e) {
            return R.failed(e.getErrorMsg() + ":" + e.getMessage());
        }
    }

    @PostMapping(value = "/data/echarts/format")
    @Operation(summary = "数据集图表格式化")
    @SaCheckClientToken(scope = "client:execute")
    public R<Output> dataSqlEchartsFormat(
            @RequestBody SqlEchartFormatDTO dto) {
        try {
            return R.ok(agentFlowService.echartsFormatBySql(dto.getUserInput()
                    , dto.getSql(), dto.getConfigs()));
        } catch (Exception e) {
            e.printStackTrace();
            throw new ValidateException(e.getMessage());
        }
    }

    @PostMapping(value = "/data/echarts/format/auto")
    @Operation(summary = "数据集图表格式化")
    @SaCheckClientToken(scope = "client:execute")
    public R<Output> dataEchartsFormat(
            @RequestBody EchartFormatDTO dto) {
        try {
            return R.ok(agentFlowService.echartsFormat(dto));
        } catch (Exception e) {
            e.printStackTrace();
            throw new ValidateException(e.getMessage());
        }
    }

    @PostMapping(value = "/data/markdown2word")
    @Operation(summary = "把markdown格式的数据转为word")
    @SaCheckClientToken(scope = "client:execute")
    public R<String> dataMarkdown2word(
            @RequestBody Markdown2wordDTO dto) {
        try {
            return R.ok(Markdown2docxUtil.buildDocFile(dto.getContent()));
        } catch (Exception e) {
            throw new ValidateException(e);
        }
    }

    @PostMapping(value = "/document/parse")
    @Operation(summary = "文档解析")
    @SaCheckClientToken(scope = "client:execute")
    @ExternalServiceLog(
            serviceType = ExternalServiceLog.ServiceType.OCR_GENERAL,
            contents = @ExternalServiceLog.Path2Type(contentPath = "$.data"),
            dataMode = ExternalServiceLog.DataMode.RESPONSE
    )
    public R<String> parseDoc(@RequestBody DocParseDTO dto) {

        if (StringUtils.isNotBlank(dto.getFileBase64())) {
            byte[] decode = Base64.getDecoder().decode(dto.getFileBase64());
            dto.setFileBytes(decode);
        }

        if (dto.getFileBytes() == null) {
            return R.failed("文件字节不能为空");
        }
        if (StringUtils.isEmpty(dto.getFileType())) {
            return R.failed("文件类型不能为空");
        }
        if (!FILE_TYPE.contains(dto.getFileType())) {
            return R.failed("文件类型不支持");
        }

        return R.ok(FileParseUtils.parseDoc(dto.getFileBytes(), dto.getFileType()));
    }

    @PostMapping(value = "/document/parseNew")
    @Operation(summary = "文档解析-新版")
    @SaCheckClientToken(scope = "client:execute")
    @ExternalServiceLog(
            serviceType = ExternalServiceLog.ServiceType.OCR_GENERAL,
            contents = @ExternalServiceLog.Path2Type(contentPath = "$.data"),
            dataMode = ExternalServiceLog.DataMode.RESPONSE
    )
    public R<String> parseDocNew(@RequestBody DocParseDTO dto) {

        if (StringUtils.isNotBlank(dto.getFileBase64())) {
            // 提取真正的Base64部分（去掉URI前缀）
            if (dto.getFileBase64().contains(",")) {
                dto.setFileBase64(dto.getFileBase64().split(",")[1]);
            }
            byte[] decode = Base64.getDecoder().decode(dto.getFileBase64());
            dto.setFileBytes(decode);
        }

        if (dto.getFileBytes() == null && StringUtils.isBlank(dto.getFileUrl())) {
            return R.failed("文件base64和文件地址不能同时为空");
        }
        if (StringUtils.isEmpty(dto.getFileType())) {
            return R.failed("文件类型不能为空");
        }
        /*if (!FILE_TYPE.contains(dto.getFileType())) {
            return R.failed("文件类型不支持");
        }*/

        return R.ok(baiduyunDocParserClient.parseDoc(dto.getFileBytes(), dto.getFileType(),dto.getFileUrl()));
    }

    @PostMapping(value = "/paper/parse")
    @Operation(summary = "试卷解析")
    @SaCheckClientToken(scope = "client:execute")
    public R<JSONObject> parsePaper(@RequestBody PaperAnalysisDTO dto) {

        if (StringUtils.isEmpty(dto.getImage()) && StringUtils.isBlank(dto.getUrl()) && StringUtils.isEmpty(dto.getPdf_file())) {
            return R.failed("文件base64和文件地址不能同时为空");
        }
        return R.ok(baiduyunDocParserClient.parsePaper(JSON.toJSONString(dto)));
    }
    @PostMapping(value = "/paper/cut-parse")
    @Operation(summary = "试卷切题解析")
    @SaCheckClientToken(scope = "client:execute")
    public R<JSONObject> parsePaperCut(@RequestBody PaperCutDTO dto) {

        if (StringUtils.isEmpty(dto.getImage()) && StringUtils.isBlank(dto.getUrl()) && StringUtils.isEmpty(dto.getPdf_file())) {
            return R.failed("文件base64和文件地址不能同时为空");
        }
        return R.ok(baiduyunDocParserClient.parsePaperCut(JSON.toJSONString(dto)));
    }

    @PostMapping(value = "/document/parse/url")
    @Operation(summary = "根据文件地址进行文档解析")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<String>> parseFileByUrl(@RequestBody PluginFileParseDTO dto) {
        log.info("根据文件地址进行文档解析,参数:{}", JSON.toJSONString(dto));
        return R.ok(FileParseUtils.parseFileByUrl(dto.getFiles()));
    }

    @PostMapping(value = "/workflow/publish/execute")
    @Operation(summary = "执行发布工作流")
    @SaCheckClientToken(scope = "client:execute")
    public R<String> executePublishedWorkflow(@Validated @RequestBody WorkflowExecuteDTO dto) {
        return R.ok(workflowService.executeWorkflow(dto));
    }

    @SneakyThrows
    @PostMapping(value = "/workflow/publish/chat/execute")
    @Operation(summary = "执行发布对话流")
    @SaCheckClientToken(scope = "client:execute")
    public SseEmitter executePublishedChatFlow(@Validated @RequestBody WorkflowExecuteDTO dto) {
        String runId = UUIDGenerator.getUUID();
        dto.setRunId(runId);
        workflowService.executeChatFlow(dto);

        String clientId = IdUtil.nanoId();
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        final SseEmitter sseEmitter = workflowService.getConn(clientId);
        CompletableFuture.runAsync(() -> workflowService.listenExecuteChatFlow(provider, clientId, runId));
        return sseEmitter;
    }


    @GetMapping("/feedback/detail")
    @Operation(summary = "反馈详情")
    @SaCheckClientToken(scope = "client:execute")
    public R<AgentHistoryFeedbackDTO> getFeedbackDetail(@NotBlank Long feedbackId) throws Exception {
        return R.ok(agentSftService.getFeedbackDetail(feedbackId));
    }
}
